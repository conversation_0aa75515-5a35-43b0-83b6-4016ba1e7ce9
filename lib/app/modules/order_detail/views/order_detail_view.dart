import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../colors.dart';
import '../../../../extension.dart';
import '../../../models/erp_category.dart';
import '../controllers/order_detail_controller.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  const OrderDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? ErpColors.darkBackground : ErpColors.background,
      body: Column(
        children: [
          // 主要內容區域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: _buildBody(context).toList(growable: false),
              ),
            ),
          ),
          // 底部操作按鈕
          _buildBottomActions(context),
        ],
      ),
    );
  }

  Iterable<Widget> _buildBody(BuildContext context) sync* {
    yield _buildTransactionTypeSelector(context);
    yield const SizedBox(height: 24);
    yield _buildAmountSection(context);
    yield const SizedBox(height: 8);
    yield _buildNumberKeypad(context);
    yield _buildCategorySelector(context);
    yield const SizedBox(height: 24);
    yield _buildDateTimeSelector(context);
    yield const SizedBox(height: 24);
    yield _buildNoteInput(context);
    yield const SizedBox(height: 24);
    yield _buildLocationSelector(context);
  }

  // 交易類型選擇器
  Widget _buildTransactionTypeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '交易類型',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkSurface : const Color(0xFFF3F4F6),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(4),
          child: Obx(() => Row(
                children: TransactionType.values.map((type) {
                  final isSelected =
                      controller.selectedTransactionType.value == type;
                  return Expanded(
                    child: GestureDetector(
                      onTap: () => controller.setTransactionType(type),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? (isDark
                                  ? ErpColors.darkCardBackground
                                  : Colors.white)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(6),
                          boxShadow: isSelected
                              ? [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ]
                              : null,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              controller.getTransactionTypeIcon(type),
                              size: 16,
                              color: isSelected
                                  ? ErpColors.primary
                                  : (isDark
                                      ? ErpColors.darkTextSecondary
                                      : ErpColors.textSecondary),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              controller.getTransactionTypeDisplayName(type),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: isSelected
                                    ? ErpColors.primary
                                    : (isDark
                                        ? ErpColors.darkTextSecondary
                                        : ErpColors.textSecondary),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              )),
        ),
      ],
    );
  }

  // 金額輸入區域
  Widget _buildAmountSection(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '金額',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Text(
                '\$',
                style: TextStyle(
                  fontSize: 24,
                  color: isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Obx(() => Text(
                      controller.amount.value,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: isDark
                            ? ErpColors.darkTextPrimary
                            : ErpColors.textPrimary,
                      ),
                    )),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 數字鍵盤
  Widget _buildNumberKeypad(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      crossAxisSpacing: 6,
      mainAxisSpacing: 6,
      childAspectRatio: 2.0,
      children: [
        // 第一行
        _buildKeypadButton('1', context),
        _buildKeypadButton('2', context),
        _buildKeypadButton('3', context),
        // 第二行
        _buildKeypadButton('4', context),
        _buildKeypadButton('5', context),
        _buildKeypadButton('6', context),
        // 第三行
        _buildKeypadButton('7', context),
        _buildKeypadButton('8', context),
        _buildKeypadButton('9', context),
        // 第四行
        _buildKeypadButton('.', context),
        _buildKeypadButton('0', context),
        _buildDeleteButton(context),
      ],
    );
  }

  // 鍵盤按鈕
  Widget _buildKeypadButton(String digit, BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: isDark ? ErpColors.darkCardBackground : Colors.white,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: () => controller.addDigit(digit),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              digit,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color:
                    isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 刪除按鈕
  Widget _buildDeleteButton(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: isDark ? const Color(0xFF2D1B1B) : const Color(0xFFFEF2F2),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: controller.deleteDigit,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isDark ? const Color(0xFF7F1D1D) : const Color(0xFFFECACA),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Icon(
              Icons.backspace,
              color: isDark ? const Color(0xFFEF4444) : const Color(0xFFDC2626),
              size: 18,
            ),
          ),
        ),
      ),
    );
  }

  // 分類選擇器
  Widget _buildCategorySelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '消費類別',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isDark ? ErpColors.darkCardBackground : Colors.white,
                border: Border.all(
                  color:
                      isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<ErpCategory>(
                  value: controller.selectedCategory.value,
                  hint: Text(
                    '請選擇類別',
                    style: TextStyle(
                      color: isDark
                          ? ErpColors.darkTextSecondary
                          : ErpColors.textSecondary,
                    ),
                  ),
                  icon: Icon(
                    Icons.keyboard_arrow_down,
                    color: isDark
                        ? ErpColors.darkTextSecondary
                        : ErpColors.textSecondary,
                  ),
                  isExpanded: true,
                  items: controller.categories.map((category) {
                    return DropdownMenuItem<ErpCategory>(
                      value: category,
                      child: Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: category.getColor().withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              category.getIcon(),
                              size: 14,
                              color: category.getColor(),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            category.name ?? '',
                            style: TextStyle(
                              color: isDark
                                  ? ErpColors.darkTextPrimary
                                  : ErpColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: controller.setCategory,
                ),
              ),
            )),
      ],
    );
  }

  // 日期時間選擇器
  Widget _buildDateTimeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '日期時間',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => GestureDetector(
              onTap: () => _showDateTimePicker(context),
              child: Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isDark ? ErpColors.darkCardBackground : Colors.white,
                  border: Border.all(
                    color:
                        isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  DateFormat('yyyy-MM-dd HH:mm')
                      .format(controller.selectedDateTime.value),
                  style: TextStyle(
                    fontSize: 16,
                    color: isDark
                        ? ErpColors.darkTextPrimary
                        : ErpColors.textPrimary,
                  ),
                ),
              ),
            )),
      ],
    );
  }

  // 備註輸入
  Widget _buildNoteInput(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '備註',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: controller.noteController,
            onChanged: controller.setNote,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '添加備註...',
              hintStyle: TextStyle(
                color: isDark
                    ? ErpColors.darkTextSecondary
                    : ErpColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
            ),
            style: TextStyle(
              color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  // 地點選擇器
  Widget _buildLocationSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '地點 (選填)',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () {
            // TODO: 實現地點選擇功能
            Get.snackbar('提示', '地點選擇功能尚未實現');
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isDark ? ErpColors.darkCardBackground : Colors.white,
              border: Border.all(
                color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Obx(() => Text(
                        controller.location.value.isEmpty
                            ? '添加地點'
                            : controller.location.value,
                        style: TextStyle(
                          color: controller.location.value.isEmpty
                              ? (isDark
                                  ? ErpColors.darkTextSecondary
                                  : ErpColors.textSecondary)
                              : (isDark
                                  ? ErpColors.darkTextPrimary
                                  : ErpColors.textPrimary),
                        ),
                      )),
                ),
                Icon(
                  Icons.chevron_right,
                  color: isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 底部操作按鈕
  Widget _buildBottomActions(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkCardBackground : Colors.white,
        border: Border(
          top: BorderSide(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: controller.cancel,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isDark ? ErpColors.darkSurface : const Color(0xFFF3F4F6),
                  foregroundColor: isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Obx(() => ElevatedButton(
                    onPressed: controller.isLoading.value
                        ? null
                        : controller.saveTransaction,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ErpColors.primary,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: controller.isLoading.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            '儲存交易',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  )),
            ),
          ],
        ),
      ),
    );
  }

  // 顯示日期時間選擇器
  Future<void> _showDateTimePicker(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: controller.selectedDateTime.value,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null && context.mounted) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(controller.selectedDateTime.value),
      );

      if (pickedTime != null) {
        final DateTime newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );
        controller.setDateTime(newDateTime);
      }
    }
  }
}
