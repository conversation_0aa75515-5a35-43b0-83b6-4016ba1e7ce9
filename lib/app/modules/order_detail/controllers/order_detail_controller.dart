import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/erp_category.dart';
import '../../../models/erp_order.dart';
import '../../../repositories/category_repository.dart';
import '../../../repositories/order_repository.dart';

enum TransactionType { expense, income, ignore }

class OrderDetailController extends GetxController {
  final CategoryRepository _categoryRepository = Get.find<CategoryRepository>();
  final OrderRepository _orderRepository = Get.find<OrderRepository>();

  // 響應式變數
  final selectedTransactionType = TransactionType.expense.obs;
  final amount = '0'.obs;
  final selectedCategory = Rxn<ErpCategory>();
  final selectedDateTime = DateTime.now().obs;
  final note = ''.obs;
  final location = ''.obs;
  final categories = <ErpCategory>[].obs;
  final isLoading = false.obs;

  // 表單控制器
  final noteController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    _loadCategories();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    noteController.dispose();
    super.onClose();
  }

  // 載入分類列表
  Future<void> _loadCategories() async {
    try {
      isLoading.value = true;
      final categoryList = await _categoryRepository.getAllAsync();
      categories.value = categoryList;
    } catch (e) {
      Get.snackbar('錯誤', '載入分類失敗: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 設置交易類型
  void setTransactionType(TransactionType type) {
    selectedTransactionType.value = type;
  }

  // 數字鍵盤輸入
  void addDigit(String digit) {
    final currentValue = amount.value;

    // 如果是小數點，檢查是否已經有小數點
    if (digit == '.' && currentValue.contains('.')) {
      return;
    }

    // 如果當前值是 "0"，且輸入的不是小數點，則替換
    if (currentValue == '0' && digit != '.') {
      amount.value = digit;
    } else {
      amount.value = currentValue + digit;
    }
  }

  // 刪除數字
  void deleteDigit() {
    final currentValue = amount.value;

    if (currentValue.isNotEmpty) {
      final newValue = currentValue.substring(0, currentValue.length - 1);
      amount.value = newValue.isEmpty ? '0' : newValue;
    }
  }

  // 設置分類
  void setCategory(ErpCategory? category) {
    selectedCategory.value = category;
  }

  // 設置日期時間
  void setDateTime(DateTime dateTime) {
    selectedDateTime.value = dateTime;
  }

  // 設置備註
  void setNote(String noteText) {
    note.value = noteText;
    noteController.text = noteText;
  }

  // 設置地點
  void setLocation(String locationText) {
    location.value = locationText;
  }

  // 儲存交易
  Future<void> saveTransaction() async {
    try {
      // 驗證必填欄位
      if (amount.value == '0' || amount.value.isEmpty) {
        Get.snackbar('錯誤', '請輸入金額');
        return;
      }

      if (selectedCategory.value == null) {
        Get.snackbar('錯誤', '請選擇分類');
        return;
      }

      isLoading.value = true;

      // 創建訂單對象
      final order = ErpOrder(
        type: _getTransactionTypeValue(),
        amount: double.tryParse(amount.value) ?? 0.0,
        note: note.value.isEmpty ? null : note.value,
        triggerAt: selectedDateTime.value,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 設置分類關聯
      order.parent.target = selectedCategory.value;

      // 儲存到資料庫
      await _orderRepository.addAsync(order);

      Get.snackbar('成功', '交易已儲存');
      Get.back();
    } catch (e) {
      Get.snackbar('錯誤', '儲存失敗: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 取消操作
  void cancel() {
    Get.back();
  }

  // 獲取交易類型數值
  int _getTransactionTypeValue() {
    switch (selectedTransactionType.value) {
      case TransactionType.expense:
        return 1; // 支出
      case TransactionType.income:
        return 2; // 收入
      case TransactionType.ignore:
        return 0; // 不計
    }
  }

  // 獲取交易類型顯示名稱
  String getTransactionTypeDisplayName(TransactionType type) {
    switch (type) {
      case TransactionType.expense:
        return '支出';
      case TransactionType.income:
        return '收入';
      case TransactionType.ignore:
        return '不計';
    }
  }

  // 獲取交易類型圖標
  IconData getTransactionTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.expense:
        return Icons.arrow_upward;
      case TransactionType.income:
        return Icons.arrow_downward;
      case TransactionType.ignore:
        return Icons.remove;
    }
  }
}
